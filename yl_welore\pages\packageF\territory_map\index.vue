<template>
	<view class="map-container">
		<!-- 地图组件 -->
		<map
			id="map"
			class="map"
			:longitude="currentLocation.longitude"
			:latitude="currentLocation.latitude"
			:scale="13"
			:markers="markers"
			:show-location="true"
			@markertap="onMarkerTap"
		>
		</map>

		<!-- 加载提示 -->
		<view v-if="loading" class="loading-mask">
			<view class="loading-content">
				<text class="loading-text">正在获取位置信息...</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				loading: true,
				// 当前位置
				currentLocation: {
					longitude: 116.397428, // 默认北京坐标
					latitude: 39.90923
				},
				// 地图标注点数据
				markers: [],
				// 静态圈子数据
				realmData: [
					{
						id: 1,
						realm_icon: 'https://img.yzcdn.cn/vant/cat.jpeg',
						realm_name: '摄影爱好者圈',
						longitude: 116.397428,
						latitude: 39.90923
					},
					{
						id: 2,
						realm_icon: 'https://img.yzcdn.cn/vant/tree.jpg',
						realm_name: '户外运动圈',
						longitude: 116.407428,
						latitude: 39.91923
					},
					{
						id: 3,
						realm_icon: 'https://img.yzcdn.cn/vant/leaf.jpg',
						realm_name: '美食分享圈',
						longitude: 116.387428,
						latitude: 39.89923
					},
					{
						id: 4,
						realm_icon: 'https://img.yzcdn.cn/vant/sand.jpeg',
						realm_name: '读书交流圈',
						longitude: 116.417428,
						latitude: 39.92923
					},
					{
						id: 5,
						realm_icon: 'https://img.yzcdn.cn/vant/water.jpeg',
						realm_name: '音乐爱好圈',
						longitude: 116.377428,
						latitude: 39.88923
					}
				]
			}
		},
		onLoad() {
			this.getCurrentLocation();
		},
		methods: {
			// 获取当前位置
			getCurrentLocation() {
				uni.getLocation({
					type: 'gcj02',
					success: (res) => {
						console.log('获取位置成功:', res);
						this.currentLocation = {
							longitude: res.longitude,
							latitude: res.latitude
						};
						this.initMarkers();
						this.loading = false;
					},
					fail: (err) => {
						console.log('获取位置失败:', err);
						uni.showToast({
							title: '位置获取失败，使用默认位置',
							icon: 'none'
						});
						this.initMarkers();
						this.loading = false;
					}
				});
			},

			// 初始化地图标注点
			initMarkers() {
				this.markers = this.realmData.map((item) => {
					return {
						id: item.id,
						longitude: item.longitude,
						latitude: item.latitude,
						iconPath: item.realm_icon,
						width: 40,
						height: 40,
						callout: {
							content: item.realm_name,
							color: '#333333',
							fontSize: 14,
							borderRadius: 8,
							bgColor: '#ffffff',
							padding: 8,
							display: 'ALWAYS'
						}
					};
				});
			},

			// 标注点点击事件
			onMarkerTap(e) {
				const markerId = e.detail.markerId;
				const realm = this.realmData.find(item => item.id === markerId);
				if (realm) {
					uni.showModal({
						title: realm.realm_name,
						content: `经度: ${realm.longitude}\n纬度: ${realm.latitude}`,
						showCancel: false
					});
				}
			}
		}
	}
</script>

<style scoped>
	.map-container {
		width: 100%;
		height: 100vh;
		position: relative;
	}

	.map {
		width: 100%;
		height: 100%;
	}

	.loading-mask {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(255, 255, 255, 0.8);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 999;
	}

	.loading-content {
		background-color: #ffffff;
		padding: 20px;
		border-radius: 8px;
		box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
	}

	.loading-text {
		color: #333333;
		font-size: 16px;
	}
</style>
